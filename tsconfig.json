{"compilerOptions": {"experimentalDecorators": true, "target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./build", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["types", "node_modules/@types"]}, "exclude": ["src/**/*.spec.ts"], "ts-node": {"files": true, "esm": true, "experimentalSpecifierResolution": "node", "compilerOptions": {"module": "ESNext", "target": "ESNext", "moduleResolution": "Node", "allowImportingTsExtensions": true}}}
{"compilerOptions": {"experimentalDecorators": true, "target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./build", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "importHelpers": false, "typeRoots": ["types", "node_modules/@types"]}, "exclude": ["src/**/*.spec.ts"], "ts-node": {"files": true, "esm": true}}
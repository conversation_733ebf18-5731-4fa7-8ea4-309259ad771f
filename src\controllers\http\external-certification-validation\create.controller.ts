import create from '../../../services/mssql/external-certification-validation/create-message.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'
import ExternalReviewCommunication, { createMessageReviewSchema } from '../../../models/external-review-communications.model.js'
import getReview from '../../../services/mssql/external-certification-validation/get-external-review.js'
import { sendGenericMessage, GenericMessage } from '@tess-f/email/dist/amqp/send-generic-message.js'
import { getByID as getLearnerProgress } from '../../../services/mssql/learner-progress/get.service.js'
import settings from '../../../config/settings.js'
import getUserById from '../../../services/mssql/users/get-by-id.service.js'
import sendNotificationService from '../../../services/amqp/notification/send-notification.service.js'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-keyword', httpLogTransformer)

/**
 * @param req.body.externalReview required fields and schema validation
 */
export default async function (req: Request, res: Response) {

  try {
    const message = new ExternalReviewCommunication(createMessageReviewSchema.parse(req.body.externalReview))
    message.fields.CreatedBy = req.session.userId
    message.fields.CreatedOn = new Date()

    //Need this information for email/notifications and previous status
    if(message.fields.ReviewId === undefined) {
      log('warn', 'Failed to create new external review communication. review id not missing.', { success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
      return
    }
    //get the attached review from the database
    const review = await getReview(message.fields.ReviewId!)

    if(review === null || review.fields.LearnerProgressId === undefined) {
      log('warn', 'Failed to create new external review communication. Review not found.', { success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
      return
    }

    const learnerProgress = await getLearnerProgress(review.fields.LearnerProgressId)
    if(learnerProgress === null) {
      log('warn', 'Failed to create new external review communication. learner progress not found.', { success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
      return
    }

    if(review.fields.StatusId === 1 || review.fields.StatusId === 4){
      message.fields.StatusId = 4
    }else if(review.fields.StatusId === 2 || review.fields.StatusId === 5){
      message.fields.StatusId = 5
    }else{
      //fallback to 1 *may need to delete else
      message.fields.StatusId = 1
    }


    // So does approve/approval/denial change always send a message
    // if this is the case we need to add a new create service for  
    console.log(review)
    console.log(learnerProgress)
    /* so who */
/*     if(learnerProgress.fields.UserID === message.fields.CreatedBy){
      //this is the learner who is submitting the external certification
      // so we send the email to the reviewers but we need to be able to get all 
      //reviewers
      const notificationResult = await sendNotificationService({
        Title: 'External Certification Review Denied',
        Message: `Your uploaded proof of completion was reviewed and denied. Please address the below comment from the review team and re-upload your proof of completion. ${message.fields.Message}`,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: ['learnerProgress.fields.UserID!']
      })
      if(!notificationResult) {
        log('warn', 'Failed to create new external review communication. notification failed.', { success: false, req })
      }
    }else{
      //this is the reviewer who is reviewing the external certification
      const userInfo = await getUserById(learnerProgress.fields.UserID!)
      if(userInfo === null) {
        log('warn', 'Failed to create new external review communication. user info not found.', { success: false, req })
        res.sendStatus(INTERNAL_SERVER_ERROR)
        return
      }
      const notificationResult = await sendNotificationService({
        Title: 'External Certification Review Denied',
        Message: `Your uploaded proof of completion was reviewed and denied. Please address the below comment from the review team and re-upload your proof of completion. ${message.fields.Message}`,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: [learnerProgress.fields.UserID!]
      })

      const emailResult = await sendGenericMessage(settings.amqp.service_queues.email, {
        to: [userInfo.Email || ''],
        message: `<p>Your uploaded proof of completion was reviewed and denied. Please address the below comment from the review team and re-upload your proof of completion.</p>
        <p>"${message.fields.Message}"</p>`,
        subject: 'Your uploaded proof of completion was denied',
        header: `<p>Your uploaded proof of completion was denied</p>`
      }, settings.amqp.command_timeout)

      if(!notificationResult || !emailResult) {
        log('warn', 'Failed to create new external review communication. notification failed.', { success: false, req })
      }
    } */
    const created = (await create(message)).fields

    log('info', 'Successfully created new external review communication', { success: true, req })

    res.json(created)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to create new external review communication due to invalid data in the request.', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else {
      log('error', 'Failed to create new external review communication.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}

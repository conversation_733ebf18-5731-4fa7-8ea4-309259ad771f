import { expect } from 'chai'
import mssql, { addRow, DB_Errors, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create-message.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { ConnectionPool } from 'mssql'
import ExternalReviewCommunicationModel from '../../../models/external-review-communications.model.js'
import { ExternalReviewCommunication, ExternalReviewCommunicationTableName } from '@tess-f/sql-tables/dist/lms/external-review-communication.js'
import { ExternalReviewReviewStatuses } from '@tess-f/sql-tables/dist/lms/external-review-status'


let pool: ConnectionPool

describe('MSSQL Learning Contexts Service', () => {

  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    pool = mssql.getPool()
  })

  it('creates external review communication message', async () => {
    const message = new ExternalReviewCommunicationModel({
      Message: 'Test Message from service',
      ReviewId: '4CEAF626-29DB-4037-9C6D-37E72441BA4C',
      StatusId: ExternalReviewReviewStatuses.PendingApproval, // This causes "int is incompatible with uniqueidentifier" error
      CreatedBy: '18536FDA-F2F2-4FC3-BE64-3A2667B6B9AE',
      CreatedOn: new Date()
    })
    
    const communicationMsg = await create(message)
    
    expect(communicationMsg.fields.Message).to.equal('Test Message from service')
  })


  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<ExternalReviewCommunication>(pool.request(), ExternalReviewCommunicationTableName, { CreatedBy: AdminUserId })
  })
})

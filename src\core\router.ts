import express, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>quest<PERSON>and<PERSON> } from 'express'
import bodyParser from 'body-parser'
import settings from '../config/settings.js'
import cookieParser from 'cookie-parser'
import pathInspector from '../controllers/http/middleware/path-inspector.js'
import checkSession from '../controllers/http/middleware/check-session.js'
import getClaims from '../controllers/http/middleware/get-claims.js'
import bodyParserMiddleware from '../controllers/http/middleware/body-parser.js'
import { metricsMiddleware } from '@tess-f/backend-utils/metrics'

// feature routes
import serviceConfigRouter from '../controllers/http/service-config/service-config.routes.js'
import LearningContextRouter from '../controllers/http/learning-contexts/learning-context.routes.js'
import AnalyticsRouter from '../controllers/http/analytics/analytics.routes.js'
import AssignmentsRouter from '../controllers/http/assignments/assignments.routes.js'
import CertificateRouter from '../controllers/http/certificate/certificate.routes.js'
import ClaimsRouter from '../controllers/http/claims/claims.routes.js'
import ContentRouter from '../controllers/http/content/content.routes.js'
import ContextLearningMatrixRouter from '../controllers/http/context-learning-matrix/context-learning-matrix.routes.js'
import DashboardRouter from '../controllers/http/dashboards/dashboard.routes.js'
import KeywordRouter from '../controllers/http/keywords/keywords.routes.js'
import LearnerProgressRouter from '../controllers/http/learner-progress/learner-progress.routes.js'
import LearningContextBookmarksRouter from '../controllers/http/learning-context-bookmarks/learning-context-bookmarks.routes.js'
import LearningContextConnectionsRouter from '../controllers/http/learning-context-connections/learning-context-connections.routes.js'
import LearningContextFavoritesRouter from '../controllers/http/learning-context-favorites/learning-context-favorites.routes.js'
import LearningContextRatingsRouter from '../controllers/http/learning-context-ratings/learning-context-ratings.routes.js'
import LearningContextSessionsRouter from '../controllers/http/learning-context-sessions/learning-context-session.routes.js'
import LearningObjectBookmarksRouter from '../controllers/http/learning-object-bookmarks/learning-object-bookmarks.routes.js'
import LearningObjectContextsRouter from '../controllers/http/learning-object-contexts/learning-object-context.routes.js'
import LearningObjectFavoritesRouter from '../controllers/http/learning-object-favorites/learning-object-favorites.routes.js'
import LearningObjectRatingsRouter from '../controllers/http/learning-object-ratings/learning-object-ratings.routes.js'
import LearningObjectsRouter from '../controllers/http/learning-objects/learning-object.routes.js'
import LocationsRouter from '../controllers/http/locations/location.routes.js'
import MyLearningRouter from '../controllers/http/my-learning/my-learning.routes.js'
import RelatedLearningRouter from '../controllers/http/related-learning/related-learning.routes.js'
import SessionEnrollmentsRouter from '../controllers/http/session-enrollments/session-enrollment.routes.js'
import SkillLevelRouter from '../controllers/http/skill-levels/skill-levels.routes.js'
import UserPreferencesRouter from '../controllers/http/user-preferences/user-preferences.routes.js'
import UsersRouter from '../controllers/http/users/users.routes.js'
import CatalogRouter from '../controllers/http/catalog/catalog.routes.js'
import UserCompletedLearningContextRouter from '../controllers/http/user-completed-learning-contexts/user-completed-learning-contexts.routes.js'
import SystemWidgetsRouter from '../controllers/http/system-widgets/system-widgets.routes.js'
import WidgetsRouter from '../controllers/http/widget/widget.routes.js'
import UserHiddenWidgetRouter from '../controllers/http/user-hidden-widgets/user-hidden-widget.routes.js'
import WidgetOrderOverrideRouter from '../controllers/http/user-widget-order-overrides/router.js'
import Scorm12InteractionRouter from '../controllers/http/scorm-1-2-interaction/scorm-1-2-interaction.routes.js'
import contextRegistrationRouter from '../controllers/http/learning-context-registration/learning-context-registration.routes.js'
import xapiVerbRouter from '../controllers/http/xapi-verb/routes.js'
import prerequisiteRouter from '../controllers/http/prerequisite/router.js'
import ExternalValidationCommunicationsRouter from '../controllers/http/external-certification-validation/external-validation.router.ts'

const router = express.Router()

router.use(bodyParser.urlencoded({ extended: false, limit: '2mb' }))
router.use(bodyParser.json({ limit: '2mb' }))
router.use(bodyParserMiddleware as ErrorRequestHandler)

// Cookie parser
router.use(cookieParser(settings.sessionAuthority.cookieSecret))

// metrics
router.use(metricsMiddleware())

// log requested path for debugging purposes (remove on prod)
router.use(pathInspector)

// Service info from settings, no token needed
// due to the way it will need to be used
router.use(serviceConfigRouter)

// token authentication middleware - All paths below here are protected
router.use(checkSession as RequestHandler)
router.use(getClaims as RequestHandler)

// feature routes
router.use(LearningContextRouter)
router.use(AnalyticsRouter)
router.use(AssignmentsRouter)
router.use(CertificateRouter)
router.use(ClaimsRouter)
router.use(ContentRouter)
router.use(ContextLearningMatrixRouter)
router.use(DashboardRouter)
router.use(ExternalValidationCommunicationsRouter)
router.use(KeywordRouter)
router.use(LearnerProgressRouter)
router.use(LearningContextBookmarksRouter)
router.use(LearningContextConnectionsRouter)
router.use(LearningContextFavoritesRouter)
router.use(LearningContextRatingsRouter)
router.use(LearningContextSessionsRouter)
router.use(LearningObjectBookmarksRouter)
router.use(LearningObjectContextsRouter)
router.use(LearningObjectFavoritesRouter)
router.use(LearningObjectRatingsRouter)
router.use(LearningObjectsRouter)
router.use(LocationsRouter)
router.use(MyLearningRouter)
router.use(RelatedLearningRouter)
router.use(SessionEnrollmentsRouter)
router.use(SkillLevelRouter)
router.use(UserPreferencesRouter)
router.use(UsersRouter)
router.use(CatalogRouter)
router.use(UserCompletedLearningContextRouter)
router.use(SystemWidgetsRouter)
router.use(WidgetsRouter)
router.use(UserHiddenWidgetRouter)
router.use(WidgetOrderOverrideRouter)
router.use(Scorm12InteractionRouter)
router.use(contextRegistrationRouter)
router.use('/xapi-verb', xapiVerbRouter)
router.use('/prerequisite', prerequisiteRouter)

export default router

import { Table } from '@lcs/mssql-utility'
import { ExternalReviewCommunication, ExternalReviewCommunicationFields, ExternalReviewCommunicationTableName } from '@tess-f/sql-tables/dist/lms/external-review-communication.js'
import z from 'zod'
import {ExternalReviewReviewStatuses} from '@tess-f/sql-tables/dist/lms/external-review-status.js'


export const createMessageSchema = z.object({
  [ExternalReviewCommunicationFields.Message]: z.string().max(500)
})

export const additionalFields = z.object({
  [ExternalReviewCommunicationFields.StatusId]: z.nativeEnum(ExternalReviewReviewStatuses).nullable().optional(), // ExternalReviewCommunicationFields
  [ExternalReviewCommunicationFields.ReviewId] : z.string().uuid(),
  [ExternalReviewCommunicationFields.CreatedBy] : z.string(),
  [ExternalReviewCommunicationFields.CreatedOn] : z.date()
})

export const createMessageReviewSchema = createMessageSchema.merge(additionalFields)
export default class ExternalReviewCommunicationModel extends Table<ExternalReviewCommunication, ExternalReviewCommunication> {
  public fields: ExternalReviewCommunication

  constructor (fields?: ExternalReviewCommunication) {
    super(ExternalReviewCommunicationTableName, [
      ExternalReviewCommunicationFields.Message,
      ExternalReviewCommunicationFields.CreatedBy,
      ExternalReviewCommunicationFields.CreatedOn,
      ExternalReviewCommunicationFields.ReviewId,
      ExternalReviewCommunicationFields.StatusId
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: ExternalReviewCommunication): void {
    this.fields = record
  }

  public exportJsonToDatabase (): ExternalReviewCommunication {
    return this.fields
  }
}

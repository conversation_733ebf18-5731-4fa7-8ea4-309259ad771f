import logger from '@lcs/logger'
import mssql from '@lcs/mssql-utility'
import { GroupClaimsTableName, GroupClaimFields } from '@tess-f/sql-tables/dist/lms/group-claim.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-MSSQL.has-claim')

export default async function (userID: string, claim: string): Promise<boolean> {
  try {
    const pool = mssql.getPool()
    const request = pool.request()
    request.input('userID', userID)
    request.input('claim', claim)

    const results = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS [TotalRecords]
    FROM [${GroupClaimsTableName}]
    WHERE [${GroupClaimFields.Claim}] = @claim
    AND [${GroupClaimFields.GroupID}] IN (
      SELECT [${UserGroupFields.GroupID}]
      FROM [${UserGroupTableName}]
      WHERE [${UserGroupFields.UserID}] = @userID
    )
  `)

    return results.recordset[0].TotalRecords > 0
  } catch (error) {
    // Unexpected error
    log('error', 'Database error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}

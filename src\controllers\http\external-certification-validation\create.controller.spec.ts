import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import ExternalReviewCommunication from '../../../models/external-review.model.js'
import { v4 as uuid } from 'uuid'

describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/external-certification-validation/create-message.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new ExternalReviewCommunication({  })))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                externalReview: {
                    Message: 'Test external review message from controller',
                    StatusId: 1,
                    ReviewId: '4CEAF626-29DB-4037-9C6D-37E72441BA4C',
                    CreatedBy: '18536FDA-F2F2-4FC3-BE64-3A2667B6B9AE',
                    CreatedOn: new Date()
                },
                session: { userId: uuid() }
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request data is invalid, missing status id', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/external-certification-validation/create-message.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new ExternalReviewCommunication({  })))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                externalReview: {
                    Message: 'Test Keyword',
                    ReviewId: '280BCCF0-7CDB-4E34-A0B7-07917AA8A0B3',
                    CreatedBy: 1,
                    CreatedOn: new Date()
                }
                ,
                session: { userId: uuid() } 
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
    })

  
})

